//
//  MessageViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yong<PERSON><PERSON> ye on 2025/3/18.
//

import UIKit

//消息中心

class MessageViewController: BaseViewController {

    // MARK: - Properties
    private var webViewController: WebViewController!

    // MARK: - 测试模块相关代码（已注释）
    /*
    private let tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.translatesAutoresizingMaskIntoConstraints = false
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "Cell")
        return tableView
    }()

    // 测试用的模块名称数组
    private let moduleNames = [
        "带货模块-变动明细",
        "金币模块-变动记录",
        "MAP",
        "互动信息",
        "新增粉丝消息",
        "分享账号",
        "视频编辑",
        "原登录页展示",
        "用户编辑页-入口",
        "播放器SDK测试",
        "用户注册选择兴趣标签",
        "调用支付宝",
        "版本检测-1.0.0",
        "笔记编辑页",
        "注销前页面",
        "微信分享测试",
        "H5消息列表"
    ]
    */

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupWebView()
        setupNotifications()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        // 确保TabBar显示状态正确
        // 由于这是一级页面（TabBar根视图控制器），需要确保TabBar显示
        print("MessageViewController viewWillAppear: 准备显示TabBar")
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 强制确保TabBar显示，特别是在登录页面关闭后
        if let tabBarController = self.tabBarController as? CustomTabBarController {
            // 检查当前是否是导航控制器的根视图控制器
            let isRootOfNavController = navigationController?.viewControllers.first == self

            if isTabBarRootViewController && isRootOfNavController {
                print("MessageViewController viewDidAppear: 强制显示TabBar")
                tabBarController.showTabBar(animated: false)

                // 确保TabBar在最上层
                tabBarController.view.bringSubviewToFront(tabBarController.customTabBar)
            }
        }
    }

    // MARK: - Setup
    private func setupUI() {
        showNavBar = false
    }

    private func setupNotifications() {
        // 监听登录成功通知，确保登录后TabBar能正确显示
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleLoginSuccess),
            name: .tokenSaved,
            object: nil
        )
    }

    @objc private func handleLoginSuccess() {
        // 登录成功后，确保TabBar显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.ensureTabBarVisible()
        }
    }

    private func ensureTabBarVisible() {
        guard let tabBarController = self.tabBarController as? CustomTabBarController else {
            return
        }

        // 检查当前是否是导航控制器的根视图控制器
        let isRootOfNavController = navigationController?.viewControllers.first == self

        if isTabBarRootViewController && isRootOfNavController {
            print("MessageViewController ensureTabBarVisible: 确保TabBar显示")
            tabBarController.showTabBar(animated: true)
            tabBarController.view.bringSubviewToFront(tabBarController.customTabBar)
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    private func setupWebView() {
        // 创建WebViewController，显示H5消息列表
        webViewController = WebViewController(path: "sxqVideo/newsList/newsList", title: "消息列表")

        // 强制隐藏WebView的导航栏，因为这是一级页面
        webViewController.hideNavBarAfterLoad = true

        // 添加为子控制器
        addChild(webViewController)
        contentView.addSubview(webViewController.view)

        // 设置WebView填充整个MessageViewController的内容区域
        webViewController.view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            webViewController.view.topAnchor.constraint(equalTo: contentView.safeAreaLayoutGuide.topAnchor),
            webViewController.view.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            webViewController.view.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            webViewController.view.bottomAnchor.constraint(equalTo: contentView.safeAreaLayoutGuide.bottomAnchor)
        ])

        // 完成子控制器添加
        webViewController.didMove(toParent: self)
    }

    /*
    // MARK: - 原TableView设置方法（已注释）
    private func setupTableView() {
        contentView.addSubview(tableView)

        // 设置 TableView 约束
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: contentView.safeAreaLayoutGuide.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: contentView.safeAreaLayoutGuide.bottomAnchor)
        ])

        // 设置代理
        tableView.delegate = self
        tableView.dataSource = self
    }
    */

    /*
    // MARK: - 微信分享测试（已注释）
    private func testWeChatShare() {
        print("=== 开始微信分享测试 ===")

        // 检查微信状态
        print("微信是否安装: \(WXApi.isWXAppInstalled())")
        print("微信是否支持API: \(WXApi.isWXAppSupportApi())")
        print("微信SDK版本: \(WXApi.getVersion())")

        // 检查微信注册状态
        print("当前注册的AppID: wx92fac135539c31c3")
        print("当前Bundle ID: \(Bundle.main.bundleIdentifier ?? "未知")")

        guard WXApi.isWXAppInstalled() else {
            showAlert(title: "提示", message: "微信未安装")
            return
        }

        guard WXApi.isWXAppSupportApi() else {
            showAlert(title: "提示", message: "微信版本不支持API")
            return
        }

        // 创建选择弹窗
        let alert = UIAlertController(title: "微信分享测试", message: "选择分享类型", preferredStyle: .actionSheet)

        // 文本分享
        alert.addAction(UIAlertAction(title: "纯文本分享", style: .default) { _ in
            self.shareText()
        })

        // 网页分享
        alert.addAction(UIAlertAction(title: "网页分享", style: .default) { _ in
            self.shareWebpage()
        })

        // 图片分享
        alert.addAction(UIAlertAction(title: "图片分享", style: .default) { _ in
            self.shareImage()
        })

        // 最简单的分享测试
        alert.addAction(UIAlertAction(title: "最简单分享测试", style: .default) { _ in
            self.shareSimplest()
        })

        // 使用URL Scheme的分享测试
        alert.addAction(UIAlertAction(title: "URL Scheme分享测试", style: .default) { _ in
            self.shareWithURLScheme()
        })

        // 直接打开微信测试
        alert.addAction(UIAlertAction(title: "直接打开微信", style: .default) { _ in
            self.openWeChat()
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        present(alert, animated: true)
    }

    // 纯文本分享
    private func shareText() {
        print("执行纯文本分享")
        print("准备发送文本到微信...")

        let req = SendMessageToWXReq()
        req.text = "这是一条来自树小柒的测试消息 🌲"
        req.bText = true
        req.scene = 0 // 好友

        print("请求参数: text=\(req.text ?? "nil"), bText=\(req.bText), scene=\(req.scene)")

        WXApi.send(req)
        print("文本分享请求已发送，等待微信响应...")
    }

    // 网页分享
    private func shareWebpage() {
        print("执行网页分享")
        print("准备发送网页到微信...")

        let webpageObject = WXWebpageObject()
        webpageObject.webpageUrl = "https://www.apple.com"

        let message = WXMediaMessage()
        message.mediaObject = webpageObject
        message.title = "苹果官网"
        message.description = "欢迎访问苹果官网，了解最新产品信息"

        // 设置缩略图
        if let image = UIImage(systemName: "applelogo") {
            message.setThumbImage(image)
            print("缩略图设置成功")
        } else {
            print("缩略图设置失败")
        }

        let req = SendMessageToWXReq()
        req.bText = false
        req.message = message
        req.scene = 0 // 好友

        print("请求参数: url=\(webpageObject.webpageUrl ?? "nil"), title=\(message.title ?? "nil"), scene=\(req.scene)")

        WXApi.send(req)
        print("网页分享请求已发送，等待微信响应...")
    }

    // 图片分享
    private func shareImage() {
        print("执行图片分享")
        print("准备发送图片到微信...")

        guard let image = UIImage(systemName: "star.fill") else {
            print("无法创建测试图片")
            return
        }

        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            print("无法转换图片数据")
            return
        }

        print("图片数据大小: \(imageData.count) bytes")

        let imageObject = WXImageObject()
        imageObject.imageData = imageData

        let message = WXMediaMessage()
        message.mediaObject = imageObject
        message.setThumbImage(image)

        let req = SendMessageToWXReq()
        req.bText = false
        req.message = message
        req.scene = 0 // 好友

        print("请求参数: imageData size=\(imageData.count), scene=\(req.scene)")

        WXApi.send(req)
        print("图片分享请求已发送，等待微信响应...")
    }

    // 最简单的分享测试
    private func shareSimplest() {
        print("=== 执行最简单分享测试 ===")

        // 创建最简单的文本分享请求
        let req = SendMessageToWXReq()
        req.text = "Hello WeChat"
        req.bText = true
        req.scene = 0

        print("发送最简单的文本分享请求...")
        WXApi.send(req)
        print("请求已发送")
    }

    // 使用URL Scheme的分享测试
    private func shareWithURLScheme() {
        print("=== 尝试重新注册微信SDK（仅URL Scheme）===")

        // 尝试仅使用URL Scheme注册（不使用Universal Links）
        if WXApi.registerApp("wx92fac135539c31c3", universalLink: "https://app.gzyoushu.com/") {
            print("✅ URL Scheme注册成功")

            // 发送分享请求
            let req = SendMessageToWXReq()
            req.text = "URL Scheme测试分享"
            req.bText = true
            req.scene = 0

            WXApi.send(req)
            print("URL Scheme分享请求已发送")
        } else {
            print("❌ URL Scheme注册失败")
        }
    }

    // 直接打开微信
    private func openWeChat() {
        print("尝试直接打开微信...")
        let success = WXApi.openWXApp()
        print("打开微信结果: \(success)")

        if !success {
            showAlert(title: "提示", message: "无法打开微信，请检查微信是否已安装")
        }
    }

    // 显示提示弹窗
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    */
}

/*
// MARK: - UITableViewDataSource（已注释）
extension MessageViewController: UITableViewDataSource {
    //高度设置一下55
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 55
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return moduleNames.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "Cell", for: indexPath)
        cell.textLabel?.text = moduleNames[indexPath.row]
        cell.accessoryType = .disclosureIndicator
        return cell
    }
}

// MARK: - UITableViewDelegate（已注释）
extension MessageViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        print("点击了模块：\(moduleNames[indexPath.row])")
        if indexPath.row == 0 {
            let vc = CommissionChangeDetailViewController()
            navigationController?.pushViewController(vc, animated: true)
        } else if indexPath.row == 1 {
            let vc = CoinTransactionRecordViewController()
            navigationController?.pushViewController(vc, animated: true)
        } else if indexPath.row == 2 {
            let vc = MapTestViewController()
            navigationController?.pushViewController(vc, animated: true)
        } else if indexPath.row == 3 {
            let vc = InteractiveInformationViewController()
            navigationController?.pushViewController(vc, animated: true)
        } else if indexPath.row == 4 {
            let vc = FollowMessageListViewController()
            navigationController?.pushViewController(vc, animated: true)
        } else if indexPath.row == 5 {
            navigationController?.pushViewController(UserSharingViewController(userId: "ff80808195fa5c680195fa7a246b0000"), animated: true)
        } else if indexPath.row == 6 {
            let vc = VideoEditViewController(videoPath: "", asset: nil, bgm: nil)
            navigationController?.pushViewController(vc, animated: true)
        } else if indexPath.row == 7 {
            let vc = LoginViewController()
            vc.modalPresentationStyle = .fullScreen
            present(vc, animated: true)
        } else if indexPath.row == 8 {
            let vc = UserInformationEditingPage()
            navigationController?.pushViewController(vc, animated: true)
        } else if indexPath.row == 9 {
            // 新增：播放器SDK测试页
            let vc = PlayerTestVC()
            navigationController?.pushViewController(vc, animated: true)
        } else if indexPath.row == 10 {
            //            navigationController?.pushViewController(PreferenceTagsViewController(), animated: true)
            let vc = PreferenceTagsViewController()
            vc.modalPresentationStyle = .fullScreen
            present(vc, animated: true)
        } else if indexPath.row == 11 {
            let jumpUrl = "https://qr.alipay.com/bax01568exhugrg9djty8081"
            //            [[UIApplication sharedApplication]openURL:[NSURL URLWithString: jumpUrl]];
            UIApplication.shared.open(URL(string: jumpUrl)!)
        } else if indexPath.row == 12 {
            APIManager.shared.getAppVersion(appVersion: "1.0.0") { [weak self] result in
                guard let self = self else { return }
                switch result {
                case .success(let response):
                    if response.isSuccess, let data = response.data.last {
                        // 有新版本，弹窗 - 取最后一条（最新版本）
                        let popupVC = UpgradePopupViewController()
                        popupVC.versionData = data
                        popupVC.modalPresentationStyle = .overFullScreen
                        self.present(popupVC, animated: true, completion: nil)
                    } else {
                        // 没有新版本，不弹窗
                        print("当前已是最新版本，无需弹窗")
                    }
                case .failure(let error):
                    print("版本检测失败: \(error)")
                }
            }
        } else if indexPath.row == 13 {
            let vc = NoteEditingDetailsViewController()
            vc.modalPresentationStyle = .fullScreen
            present(vc, animated: true)
        } else if indexPath.row == 15 {
            // 微信分享测试
            testWeChatShare()
        } else if indexPath.row == 16 {
            // H5消息列表
            let vc = WebViewController(path: "sxqVideo/newsList/newsList", title: "消息列表")
            navigationController?.pushViewController(vc, animated: true)
        } else if indexPath.row == 14 {

            let vc = DeleteAccountViewController()
            navigationController?.pushViewController(vc, animated: true)
        }
    }
}
*/
